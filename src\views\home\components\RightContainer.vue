<template>
  <div class="right-container">
    <on-duty-personnel />
    <alarm-list />
    <building-overview />
  </div>
</template>

<script setup lang="ts">
import OnDutyPersonnel from './OnDutyPersonnel.vue'
import AlarmList from './AlarmList.vue'
import BuildingOverview from './BuildingOverview.vue'
</script>

<style scoped lang="less">
.right-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
  overflow-y: auto;
  
  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(0, 26, 40, 0.3);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(78, 237, 255, 0.3);
    border-radius: 4px;
    
    &:hover {
      background: rgba(78, 237, 255, 0.5);
    }
  }
}
</style>
