<template>
  <div>
    <SearchForm
      :fields="searchFields"
      :initialValues="initialValues"
      :defaultTimeRange="'month'"
      @search="handleSearch"
      @reset="handleReset"
      @timeRangeChange="handleTimeRangeChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import SearchForm from '@/components/SearchForm.vue';

const searchFields = ref([
  {
    type: 'select',
    prop: 'businessType',
    formItem: { label: '业务类型' },
    attrs: {
      placeholder: '请选择',
      clearable: true,
    },
    options: [
      { label: '类型1', value: '1' },
      { label: '类型2', value: '2' },
    ],
    on: {
      change: (val) => {
        console.log('业务类型变化:', val);
      },
      clear: () => {
        console.log('业务类型清空');
      },
    },
  },
  {
    type: 'input',
    prop: 'keyword',
    formItem: { label: '关键词' },
    attrs: {
      placeholder: '请输入关键词',
      clearable: true,
    },
  },
]);

const initialValues = ref({
  businessType: '1',
});

const handleSearch = (values) => {
  console.log('搜索参数:', values);
};

const handleReset = () => {
  console.log('重置搜索');
};

const handleTimeRangeChange = (timeRange) => {
  console.log('时间范围变化:', timeRange);
};
</script>